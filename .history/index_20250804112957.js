const fs = require('fs')

// 读取规则文件
function loadRules() {
    try {
        const rulesData = fs.readFileSync('rule.json', 'utf8')
        return JSON.parse(rulesData)
    } catch (error) {
        console.error('读取规则文件失败:', error.message)
        return {}
    }
}

// 解析chain.txt文件内容
function parseChainData(content) {
    const lines = content.split('\n').map(line => line.trim()).filter(line => line)
    const data = {}

    for (let i = 0 i < lines.length i++) {
        const line = lines[i]
        if (i + 1 < lines.length) {
            const nextLine = lines[i + 1]
            data[line] = nextLine
        }
    }

    return data
}

// 处理单个币种信息
function processCoinInfo(data, rules) {
    const result = {}

    // 1. 提取币种名称
    let coinName = ''
    if (data['链名(对内)']) {
        coinName = data['链名(对内)']
    } else if (data['币种']) {
        coinName = data['币种'].toLowerCase()
    }

    // 2. 提取合约地址
    let contractAddress = data['合约地址'] || ''
    const coinType = data['币种类型'] || ''

    // 如果是ERC20且包含0x开头，则去掉0x
    if (coinType === 'ERC20' && contractAddress.startsWith('0x')) {
        contractAddress = contractAddress.substring(2).toLowerCase()
    }

    // 3. 提取币种精度
    const precision = parseInt(data['币种精度']) || 18

    // 4. 转换币种类型
    const chainType = rules[coinType] || coinType.toLowerCase()

    // 生成key和value
    const key = `${chainType}.assets.${coinName}`
    const value = {
        precision: precision,
        contract_address: contractAddress
    }

    return { key, value }
}

// 主函数
function main() {
    try {
        // 读取chain.txt文件
        if (!fs.existsSync('chain.txt')) {
            console.error('chain.txt 文件不存在')
            return
        }

        const content = fs.readFileSync('chain.txt', 'utf8')
        const rules = loadRules()

        // 按空行分割，处理多个币种信息
        const sections = content.split(/\n\s*\n/).filter(section => section.trim())

        sections.forEach((section, index) => {
            console.log(`\n=== 处理第 ${index + 1} 个币种信息 ===`)

            const data = parseChainData(section)
            const result = processCoinInfo(data, rules)

            console.log(`key：${result.key}`)
            console.log(`value：${JSON.stringify(result.value)}`)
        })

    } catch (error) {
        console.error('处理失败:', error.message)
    }
}

// 运行主函数
if (require.main === module) {
    main()
}

module.exports = { parseChainData, processCoinInfo, loadRules }
